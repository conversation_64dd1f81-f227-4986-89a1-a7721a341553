<p align="center">
  <img width="328px" src="https://nuxtjs.org/logos/nuxt.svg">
</p>

# 🥛 Milku - Modern Milk Delivery Web App

A full-stack web application for browsing, selecting, and ordering premium milk products.  
Built with **Nuxt.js 2** (Vue.js), **Express.js**, and **MongoDB**,
This project showcases a modern web stack with a focus on user experience, security, and performance.

---

## 🚀 Features

- Beautiful hero landing page with product highlights
- Browse a curated selection of milk products
- Add products to cart and manage your order
- User authentication (login/register)
- Secure backend API with Express.js & MongoDB (via Mongoose)
- Rate limiting and input validation for security
- Responsive design and accessible UI
- FontAwesome icon support

---

## 🖼️ Preview

![Milku Hero](static/hero-milk.png)

---

## 📦 Project Structure

- `api/` - Express.js server and API routes
- `pages/` - Nuxt.js pages (Vue components)
- `components/` - Reusable Vue components
- `store/` - Vuex store for state management
- `static/` - Static assets (e.g., hero images)
- `assets/` - Uncompiled assets (styles, images)

---

## 🛠️ Getting Started

```bash
# Clone the repository
git clone 
cd milku

# Install dependencies
yarn install # or npm install

# Start the development server
yarn dev
```

Visit [http://localhost:3000](http://localhost:3000) to view the app.

---

## 📝 Commands

| Command         | Description                                                     |
|-----------------|-----------------------------------------------------------------|
| `yarn dev`      | Start ExpressJS server with Nuxt.js in development mode         |
| `yarn build`    | Build the Nuxt.js web application for production                |
| `yarn start`    | Start ExpressJS server in production                            |
| `yarn generate` | Generate a static version of the application                    |
| `yarn lint`     | Run ESLint to check code quality                                |

---

## ⚡ Upgrading to Nuxt 3

This project uses Nuxt 2. To upgrade to Nuxt 3:

1. Install Nuxt Bridge:
   ```bash
   yarn add --dev @nuxt/bridge nuxi
   ```
2. Update your `nuxt.config.js`:
   ```js
   import { defineNuxtConfig } from '@nuxt/bridge'
   export default defineNuxtConfig({
     bridge: true
   })
   ```
3. Update your scripts in `package.json`:
   ```json
   {
     "scripts": {
       "dev": "nuxi dev",
       "build": "nuxi build",
       "start": "nuxi preview"
     }
   }
   ```
See the [Nuxt 3 migration guide](https://nuxt.com/docs/migration/overview) for more details.

---

## 📚 Documentation

- [ExpressJS Routing](http://expressjs.com/en/guide/routing.html)
- [Nuxt.js Guide](https://nuxtjs.org/guide/)
- [Vue.js Guide](http://vuejs.org/guide/)
- [MongoDB with Mongoose](https://mongoosejs.com/docs/guide.html)
- [FontAwesome Icons](https://fontawesome.com/icons)
- [ESLint Configuration](https://eslint.org/docs/user-guide/configuring)


# 🛠️ Contributing
Contributions are welcome! Please read the [contributing guidelines](CONTRIBUTING.md) before submitting a pull request.

# 📄 License
This project is licensed under the MIT License. See the [LICENSE](LICENSE) file for details.
