const express = require('express')
const bodyParser = require('body-parser')
const connectDB = require('./db/connection')
const cors = require('cors')
const { apiLimiter } = require('./middleware/rateLimit')

// Create express instance
const app = express()

// CORS configuration
const corsOptions = {
  origin: process.env.NODE_ENV === 'production'
    ? process.env.FRONTEND_URL || 'https://yourdomain.com'
    : 'http://localhost:3000',
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
};

// Middleware
app.use(cors(corsOptions))
app.use(bodyParser.json())
app.use(bodyParser.urlencoded({ extended: true }))
app.use(apiLimiter) // Apply rate limiting to all API routes

// Connect to MongoDB
connectDB()

// Require API routes
const users = require('./routes/users')
const test = require('./routes/test')
const milk = require('./routes/milk')

// Import API Routes
app.use(users)
app.use(test)
app.use(milk)

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack)
  res.status(500).json({ message: 'Something went wrong!' })
})

// Export express app
module.exports = app

// Start standalone server if directly running
if (require.main === module) {
  const port = process.env.PORT || 3001
  app.listen(port, () => {
    console.log(`API server listening on port ${port}`)
  })
}


