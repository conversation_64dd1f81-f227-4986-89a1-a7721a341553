<template>
  <footer class="footer">
    <div class="footer-content">
      <div class="footer-section about">
        <div class="logo-container">
          <img src="/assets/milk-icon.svg" alt="Milku Logo" class="footer-logo">
          <h3>Milku</h3>
        </div>
        <p>The finest selection of milk products delivered to your door.</p>
        <SocialLinks />
      </div>
      <div class="footer-section links">
        <h3>Quick Links</h3>
        <ul>
          <li><nuxt-link to="/">Home</nuxt-link></li>
          <li><nuxt-link to="/milk">Shop</nuxt-link></li>
          <li><nuxt-link to="/cart">Cart</nuxt-link></li>
        </ul>
      </div>

      <div class="footer-section contact">
        <h3>Contact Us</h3>
        <p><FontAwesomeIcon :icon="['fas', 'map-marker-alt']" /> 123 Dairy Lane, Milk City</p>
        <p><FontAwesomeIcon :icon="['fas', 'phone']" /> (*************</p>
        <p><FontAwesomeIcon :icon="['fas', 'envelope']" /> <EMAIL></p>
      </div>
    </div>

    <div class="footer-bottom">
      <p>&copy; {{ currentYear }} Milku. All rights reserved.</p>
    </div>
  </footer>
</template>

<script>
import SocialLinks from './SocialLinks.vue'

export default {
  name: 'Footer',
  components: {
    SocialLinks
  },
  computed: {
    currentYear() {
      return new Date().getFullYear()
    }
  }
}
</script>

<style scoped>
.footer {
  background-color: #f8f8f8;
  border-top: 1px solid #e7e7e7;
  color: #333;
  font-size: 0.9rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.footer-section {
  padding: 0 1rem;
}

.footer-section h3 {
  margin-bottom: 1rem;
  color: #35495e;
  font-size: 1.2rem;
}

.logo-container {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.footer-logo {
  height: 30px;
  width: 30px;
  margin-right: 10px;
}

.links ul {
  list-style: none;
  padding: 0;
}

.links li {
  margin-bottom: 0.5rem;
}

.links a {
  color: #3b8070;
  text-decoration: none;
  transition: color 0.3s ease;
}

.links a:hover {
  color: #2d6a5a;
  text-decoration: underline;
}

.contact p {
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
}

.contact svg {
  margin-right: 0.5rem;
  color: #3b8070;
}

.footer-bottom {
  text-align: center;
  padding: 1rem;
  background-color: #f0f0f0;
  border-top: 1px solid #e7e7e7;
}

@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .logo-container {
    justify-content: center;
  }

  .contact p {
    justify-content: center;
  }
}
</style>
