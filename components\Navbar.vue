<template>
  <nav class="navbar">
    <div class="navbar-brand">
      <nuxt-link to="/" class="brand">
        <img src="/assets/milk-icon.svg" alt="Milku Logo" class="logo">
        <span class="brand-name"><PERSON><PERSON></span>
      </nuxt-link>
    </div>
    <div class="nav-links">
      <nuxt-link to="/" class="nav-link">Home</nuxt-link>
      <nuxt-link to="/milk" class="nav-link">Shop</nuxt-link>

      <!-- Auth links -->
      <template v-if="$auth.loggedIn">
        <nuxt-link to="/profile" class="nav-link">My Profile</nuxt-link>
        <a @click.prevent="logout" class="nav-link">Logout</a>
      </template>
      <template v-else>
        <nuxt-link to="/login" class="nav-link">Login</nuxt-link>
        <nuxt-link to="/register" class="nav-link">Register</nuxt-link>
      </template>

      <nuxt-link to="/cart" class="nav-link cart-link">
        <font-awesome-icon :icon="['fas', 'cart-shopping']" style="color: black;" />
        <span v-if="cartCount" class="cart-count">{{ cartCount }}</span>
      </nuxt-link>
    </div>
  </nav>
</template>

<script>
export default {
  computed: {
    cartCount() {
      return this.$store.state.cart ?
        this.$store.state.cart.reduce((sum, item) => sum + item.quantity, 0) : 0
    }
  },
  methods: {
    async logout() {
      await this.$auth.logout()
      this.$router.push('/')
    }
  }
}
</script>

<style scoped>
.navbar {
  padding: 1rem 2rem;
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(8px);
  border-bottom: 1px solid #e5e7eb;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 100;
}

.navbar-brand {
  display: flex;
  align-items: center;
}

.brand {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #35495e;
}

.logo {
  height: 40px;
  width: 40px;
  margin-right: 10px;
}

.brand-name {
  font-size: 1.5rem;
  font-weight: bold;
}

.nav-links {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.nav-link {
  color: #35495e;
  text-decoration: none;
  font-size: 1.1rem;
  position: relative;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: #3b8070;
}

.cart-link {
  display: flex;
  align-items: center;
}

.cart-count {
  background-color: #3b8070;
  color: white;
  border-radius: 50%;
  padding: 0.2rem 0.5rem;
  font-size: 0.8rem;
  margin-left: 0.5rem;
}

.cart-link svg {
  margin-right: 0.5rem;
  width: 1em; /* Ensure SVG scales properly */
  height: 1em;
}

@media (max-width: 600px) {
  .navbar {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
  }

  .nav-links {
    gap: 1rem;
  }

  .brand-name {
    font-size: 1.2rem;
  }
}
</style>
