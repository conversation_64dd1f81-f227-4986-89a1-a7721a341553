{"name": "milku-web", "version": "1.5.0", "private": true, "description": "", "main": "index.js", "author": "<PERSON><PERSON><PERSON><PERSON>", "scripts": {"dev": "nuxt", "build": "nuxt build", "start": "nuxt start", "generate": "nuxt generate", "lint": "yarn lint:js", "lint:js": "eslint --ext .js,.vue --ignore-path .gitignore ."}, "engines": {"node": ">=22.14.0", "yarn": ">=1.22"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^3.0.8", "@nuxtjs/auth-next": "^5.0.0-**********.dfbbb54", "@nuxtjs/axios": "^5.13.6", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.2", "mongodb": "^6.16.0", "mongoose": "^8.14.1", "node-cache": "^5.1.2", "nuxt": "^2.15.8"}, "devDependencies": {"@nuxtjs/eslint-config": "^5.0.0", "@nuxtjs/eslint-module": "^3.0.2", "babel-eslint": "^10.1.0", "eslint": "^7.20.0", "eslint-plugin-nuxt": "^2.0.0"}, "license": "MIT"}