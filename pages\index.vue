<template>
  <div class="home-container">
    <section class="hero-section">
      <div class="hero-bg"></div>
      <img src="/hero-milk.png" alt="Almond Blue Milku" class="hero-image" />
      <div class="hero-text">
        <h1 class="title">
          Almond <span class="blue">BLUE</span> MILKU
        </h1>
        <p class="subtitle">The finest selection of milk products delivered to your door</p>
        <nuxt-link to="/milk" class="button--green">Browse Our Selection</nuxt-link>
      </div>
      <div class="hero-bubbles">
        <span class="bubble bubble1"></span>
        <span class="bubble bubble2"></span>
        <span class="bubble bubble3"></span>
      </div>
    </section>

    <div class="features">
      <div class="feature">
        <FontAwesomeIcon :icon="['fas', 'truck']" class="feature-icon" />
        <h3>Fast Delivery</h3>
        <p>Get your milk delivered within 24 hours</p>
      </div>

      <div class="feature">
        <FontAwesomeIcon :icon="['fas', 'leaf']" class="feature-icon" />
        <h3>Eco-Friendly</h3>
        <p>Sustainable packaging for all our products</p>
      </div>

      <div class="feature">
        <FontAwesomeIcon :icon="['fas', 'heart']" class="feature-icon" />
        <h3>Quality Guaranteed</h3>
        <p>Only the freshest milk products</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  layout: 'default',
  head() {
    return {
      title: 'Milku - Home',
      meta: [
        { hid: 'description', name: 'description', content: 'The finest selection of milk products delivered to your door' }
      ]
    }
  }
}
</script>

<style scoped>
.home-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.hero-section {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 520px;
  background: #eaf6fa;
  border-radius: 24px;
  overflow: hidden;
  margin-bottom: 4rem;
  box-shadow: 0 8px 32px rgba(0,0,0,0.10);
}

.hero-bg {
  position: absolute;
  inset: 0;
  background: radial-gradient(circle at 70% 30%, #b3e0ff 0%, #eaf6fa 70%);
  z-index: 1;
  pointer-events: none;
}

.hero-image {
  position: relative;
  z-index: 2;
  width: 60%;
  max-width: 700px;
  object-fit: contain;
  border-radius: 24px;
  box-shadow: 0 12px 40px rgba(34,103,122,0.18), 0 2px 8px rgba(0,0,0,0.08);
  transform: rotate(-8deg) scale(1.04);
  transition: transform 0.4s cubic-bezier(.4,2,.6,1);
  background: rgba(255,255,255,0.7);
}

.hero-section:hover .hero-image {
  transform: rotate(-4deg) scale(1.08);
  box-shadow: 0 20px 60px rgba(34,103,122,0.22), 0 4px 16px rgba(0,0,0,0.12);
}

.hero-text {
  position: absolute;
  right: 5%;
  bottom: 10%;
  background: rgba(255,255,255,0.92);
  padding: 2rem 2.5rem;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  text-align: left;
  z-index: 3;
  max-width: 350px;
}

.title {
  font-size: 2.7rem;
  color: #35495e;
  margin-bottom: 1rem;
  font-weight: bold;
  letter-spacing: 1px;
  line-height: 1.1;
}

.title .blue {
  color: #22677a;
  font-weight: 700;
  letter-spacing: 2px;
}

.subtitle {
  font-size: 1.25rem;
  color: #606f7b;
  margin-bottom: 1.5rem;
}

.button--green {
  background-color: #3b8070;
  color: #fff;
  padding: 0.75rem 1.5rem;
  border-radius: 0.25rem;
  text-decoration: none;
  font-size: 1.2rem;
  transition: background-color 0.3s, box-shadow 0.3s;
  display: inline-block;
  box-shadow: 0 2px 8px rgba(59,128,112,0.10);
}

.button--green:hover {
  background-color: #2d6a5a;
  box-shadow: 0 4px 16px rgba(59,128,112,0.18);
}

.hero-bubbles {
  position: absolute;
  left: 5%;
  top: 10%;
  z-index: 2;
  pointer-events: none;
}

.bubble {
  position: absolute;
  border-radius: 50%;
  opacity: 0.25;
  background: #b3e0ff;
  animation: float 6s infinite ease-in-out;
}

.bubble1 {
  width: 60px;
  height: 60px;
  left: 0;
  top: 0;
  animation-delay: 0s;
}

.bubble2 {
  width: 40px;
  height: 40px;
  left: 80px;
  top: 40px;
  animation-delay: 2s;
}

.bubble3 {
  width: 30px;
  height: 30px;
  left: 40px;
  top: 100px;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0) scale(1);}
  50% { transform: translateY(-30px) scale(1.1);}
}

.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.feature {
  text-align: center;
  padding: 2rem;
  border-radius: 8px;
  background-color: #f8f9fa;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  font-size: 2.5rem;
  color: #3b8070;
  margin-bottom: 1rem;
}

.feature h3 {
  margin-bottom: 0.5rem;
  color: #35495e;
}

.feature p {
  color: #606f7b;
}

@media (max-width: 900px) {
  .hero-section {
    flex-direction: column;
    min-height: 400px;
  }
  .hero-image {
    width: 100%;
    max-width: 100%;
    margin-bottom: 1.5rem;
  }
  .hero-text {
    position: static;
    margin-top: -4rem;
    width: 90%;
    text-align: center;
    max-width: 100%;
  }
  .hero-bubbles {
    left: 10%;
    top: 5%;
  }
}

@media (max-width: 768px) {
  .title {
    font-size: 2.1rem;
  }

  .subtitle {
    font-size: 1.1rem;
  }
}
</style>
