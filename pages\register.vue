<template>
  <div class="register-container">
    <div class="register-form">
      <h1>Register</h1>
      <div v-if="error" class="error-message">{{ error }}</div>

      <form @submit.prevent="register">
        <div class="form-group">
          <label for="name">Name</label>
          <input
            id="name"
            v-model="name"
            type="text"
            required
            placeholder="Enter your name"
          >
        </div>

        <div class="form-group">
          <label for="email">Email</label>
          <input
            id="email"
            v-model="email"
            type="email"
            required
            placeholder="Enter your email"
          >
        </div>

        <div class="form-group">
          <label for="password">Password</label>
          <input
            id="password"
            v-model="password"
            type="password"
            required
            placeholder="Enter your password (min 6 characters)"
            minlength="6"
          >
        </div>

        <button type="submit" class="button--green" :disabled="loading">
          {{ loading ? 'Registering...' : 'Register' }}
        </button>
      </form>

      <div class="login-link">
        Already have an account? <nuxt-link to="/login">Login</nuxt-link>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  middleware: 'guest',
  data() {
    return {
      name: '',
      email: '',
      password: '',
      error: null,
      loading: false
    }
  },
  methods: {
    async register() {
      this.error = null
      this.loading = true

      try {
        // Register the user
        await this.$axios.post('/api/users/register', {
          name: this.name,
          email: this.email,
          password: this.password
        })

        // Login with the newly created credentials
        await this.$auth.loginWith('local', {
          data: {
            email: this.email,
            password: this.password
          }
        })

        this.$router.push('/')
      } catch (error) {
        this.error = error.response?.data?.message ||
                   error.response?.data?.errors?.[0]?.msg ||
                   'Registration failed'
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.register-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80vh;
}

.register-form {
  width: 100%;
  max-width: 400px;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  background-color: white;
}

h1 {
  margin-bottom: 20px;
  color: #41b883;
}

.form-group {
  margin-bottom: 15px;
  text-align: left;
}

label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.button--green {
  display: block;
  width: 100%;
  padding: 12px;
  background-color: #41b883;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  margin-top: 20px;
}

.button--green:disabled {
  background-color: #a8d5c2;
  cursor: not-allowed;
}

.error-message {
  color: #e74c3c;
  margin-bottom: 15px;
  padding: 10px;
  background-color: #fdecea;
  border-radius: 4px;
}

.login-link {
  margin-top: 20px;
  font-size: 14px;
}

.login-link a {
  color: #41b883;
  text-decoration: none;
}
</style>
