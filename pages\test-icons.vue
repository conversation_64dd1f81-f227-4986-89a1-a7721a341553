<template>
  <div class="test-container">
    <h1>FontAwesome Icon Test</h1>

    <div class="icon-grid">
      <div class="icon-item">
        <FontAwesomeIcon :icon="['fas', 'cart-shopping']" size="2x" />
        <p>Cart Shopping</p>
      </div>

      <div class="icon-item">
        <FontAwesomeIcon :icon="['fas', 'truck']" size="2x" />
        <p>Truck</p>
      </div>

      <div class="icon-item">
        <FontAwesomeIcon :icon="['fas', 'leaf']" size="2x" />
        <p>Leaf</p>
      </div>

      <div class="icon-item">
        <FontAwesomeIcon :icon="['fas', 'heart']" size="2x" />
        <p>Heart</p>
      </div>

      <div class="icon-item">
        <FontAwesomeIcon :icon="['fab', 'facebook']" size="2x" />
        <p>Facebook</p>
      </div>

      <div class="icon-item">
        <FontAwesomeIcon :icon="['fas', 'envelope']" size="2x" />
        <p>Envelope</p>
      </div>
    </div>

    <div class="test-info">
      <h2>Test Results:</h2>
      <p>If you can see icons above, FontAwesome is working correctly!</p>
      <p>If you see empty spaces or squares, there's still an issue.</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TestIcons'
}
</script>

<style scoped>
.test-container {
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.icon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 2rem;
  margin: 2rem 0;
}

.icon-item {
  text-align: center;
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #f9f9f9;
}

.icon-item svg {
  color: #3b8070;
  margin-bottom: 0.5rem;
}

.test-info {
  margin-top: 2rem;
  padding: 1rem;
  background: #e8f4f8;
  border-radius: 8px;
}

h1 {
  color: #35495e;
  margin-bottom: 1rem;
}

h2 {
  color: #35495e;
  margin-bottom: 0.5rem;
}

p {
  color: #606f7b;
  margin-bottom: 0.5rem;
}
</style>
