import Vue from 'vue'
import { config, library } from '@fortawesome/fontawesome-svg-core'
import {
  faCartShopping,
  faTruck,
  faLeaf,
  faHeart,
  faMapMarkerAlt,
  faPhone,
  faEnvelope,
  faStar,
  faCheckCircle
} from '@fortawesome/free-solid-svg-icons'
import {
  faFacebook,
  faTwitter,
  faInstagram,
  faLinkedin
} from '@fortawesome/free-brands-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'

// This is important, we are going to let Nuxt.js worry about the CSS
config.autoAddCss = false

library.add(
  faCartShopping,
  faTruck,
  faLeaf,
  faHeart,
  faMapMarkerAlt,
  faPhone,
  faEnvelope,
  faStar,
  faCheckCircle,
  faFacebook,
  faTwitter,
  faInstagram,
  faLinkedin
)

Vue.component('FontAwesomeIcon', FontAwesomeIcon)
